"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@ragtop-web/ui/components/command"
import { Popover, PopoverContent, PopoverTrigger } from "@ragtop-web/ui/components/popover"
import { Loader2, Check, ChevronsUpDown } from "lucide-react"
import { type User } from "../page"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@ragtop-web/ui/components/select"
import { useModelTypes } from "@/service/model-service"
import { cn } from "@ragtop-web/ui/lib/utils"


// 防抖函数
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(timer)
    }
  }, [value, delay])

  return debouncedValue
}

const createFormSchema = (isCreating:boolean)=> z.object({
  name: z.string().min(1, "团队名称不能为空"),
  admin_user_id: z.string().min(1, "请选择团队管理员"),
  chat_id: isCreating ? z.string().min(1, "请选择适当的模型"):z.string().optional(),
  embd_id: isCreating ? z.string().min(1, "请选择适当的模型"):z.string().optional(),
  rerank_id: z.string().optional(),
})

type FormValues = z.infer<ReturnType<typeof createFormSchema>>

// 团队数据（用于表单）
interface TeamData {
  id?: string
  name: string
  admin_user_id: string
}

interface TeamFormProps {
  teamData?: TeamData
  onSave: (formData: { name: string; admin_user_id: string }) => void
  isCreating: boolean
  viewOnly?: boolean
  queryUsers: (params: { page_number?: number; page_size?: number; keyword?: string }) => Promise<any>
}

export function TeamForm({ teamData, onSave, isCreating, viewOnly = false, queryUsers }: TeamFormProps) {
  // 用户搜索状态
  const [open, setOpen] = useState(false)
  const [userKeyword, setUserKeyword] = useState("")
  const [users, setUsers] = useState<User[]>([])
  const [isLoadingUsers, setIsLoadingUsers] = useState(false)
  const [userPage, setUserPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { chatOption, embdOption, rerankOption } = useModelTypes()
  const formSchema = createFormSchema(isCreating);
  // 滚动加载引用
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // 使用防抖处理搜索关键词
  const debouncedKeyword = useDebounce(userKeyword, 800)

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: teamData
      ? {
        name: teamData.name,
        admin_user_id: teamData.admin_user_id,
      }
      : {
        name: "",
        admin_user_id: "",
      },
  })

  // 加载用户列表
  const loadUsers = useCallback(async (page = 1, keyword = "", append = false) => {
    if (page === 1) {
      setHasMore(true)
    }

    // 避免重复加载
    if (isLoadingUsers) return;

    setIsLoadingUsers(true)
    try {
      const response = await queryUsers({
        page_number: page,
        page_size: 10,
        keyword
      })

      const newUsers = response.records || []
      if (append) {

        console.log(newUsers)
        setUsers(prev => [...prev, ...newUsers])
      } else {
        setUsers(newUsers)
      }

      setUserPage(page)

      // 检查是否还有更多数据
      setHasMore(newUsers.length > 0 && (page * 10) < (response.total || 0))
    } catch (error) {
      console.error("加载用户失败", error)
    } finally {
      setIsLoadingUsers(false)
    }
  }, [queryUsers, isLoadingUsers])

  // 加载更多用户
  const loadMoreUsers = useCallback(() => {
    if (!isLoadingUsers && hasMore) {
      loadUsers(userPage + 1, debouncedKeyword, true)
    }
  }, [loadUsers, isLoadingUsers, hasMore, userPage, debouncedKeyword])

  // 设置滚动观察器
  useEffect(() => {
    if (loadMoreRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries.length > 0 && entries[0]?.isIntersecting && hasMore && !isLoadingUsers) {
            loadMoreUsers()
          }
        },
        { threshold: 0.1, rootMargin: "50px" }
      )

      observerRef.current.observe(loadMoreRef.current)
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [hasMore, isLoadingUsers])

  // 监听滚动事件 - 优化滚动检测
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    // 避免重复触发
    if (isLoadingUsers || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    // 当滚动到距离底部 100px 时加载更多
    if (scrollHeight - scrollTop - clientHeight < 100) {
      loadMoreUsers();
    }
  }, [loadMoreUsers, isLoadingUsers, hasMore]);

  // 监听下拉框打开状态，初始加载数据
  useEffect(() => {
    if (open) {
      // 只在打开下拉框时加载初始数据
      loadUsers(1, "", false);
    }
  }, [open])

  // 监听防抖后的关键词变化，自动搜索（仅当下拉框打开且关键词变化时）
  useEffect(() => {
    if (!open) return;
    // 避免初始加载时重复请求
    if (debouncedKeyword === "") return;

    // 当关键词变化时，自动执行搜索
    loadUsers(1, debouncedKeyword, false);

  }, [debouncedKeyword, open])



  // 保存团队
  const handleSubmit = (values: FormValues) => {
    setIsSubmitting(true)

    // 调用保存函数
    try {
      onSave(values)
    } catch (error) {
      console.error("保存团队失败", error)
    } finally {
      // 延迟重置提交状态，以便显示加载效果
      setTimeout(() => {
        setIsSubmitting(false)
      }, 500)
    }
  }

  return (

    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>团队名称</FormLabel>
              <FormControl>
                <Input
                  placeholder="输入团队名称"
                  {...field}
                  disabled={viewOnly}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="admin_user_id"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>团队管理员</FormLabel>
              <Popover open={open} onOpenChange={setOpen} modal={true}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      role="combobox"
                      disabled={viewOnly}
                      className={cn(
                        "w-full justify-between",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        users.find((user) => user.id === field.value)?.login_name ||
                        "选择团队管理员"
                      ) : (
                        "选择团队管理员"
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" style={{ width: "var(--radix-popover-trigger-width)" }}>
                  <Command className="w-full">
                    <CommandInput
                      placeholder="搜索用户..."
                      value={userKeyword}
                      onValueChange={setUserKeyword}
                    />
                    {isLoadingUsers && userKeyword.length > 0 && (
                      <Loader2 className="h-4 w-4 animate-spin opacity-70" />
                    )}
                    <CommandList
                      onScroll={handleScroll}>
                      <CommandEmpty>
                        {isLoadingUsers && userKeyword.length === 0 ? (
                          <div className="flex items-center justify-center py-6">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>加载中...</span>
                          </div>
                        ) : isLoadingUsers && userKeyword.length > 0 ? (
                          <div className="flex items-center justify-center py-6">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>搜索中...</span>
                          </div>
                        ) : (
                          <div className="py-6 text-center">没有找到用户</div>
                        )}
                      </CommandEmpty>
                      <CommandGroup>
                        {users.map((user) => (
                          <CommandItem
                            key={user.id}
                            value={user.id}
                            onSelect={() => {
                              field.onChange(user.id)
                              setOpen(false)
                            }}
                            className="flex items-center justify-between"
                          >
                            <div className="flex items-center">
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  field.value === user.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              <span>{user.login_name || user.nick || user.id}</span>
                            </div>
                          </CommandItem>
                        ))}

                        {/* 加载更多指示器 */}
                        {users.length > 0 && (
                          <div
                            ref={loadMoreRef}
                            className="py-2 text-center text-sm text-muted-foreground"
                          >
                            {isLoadingUsers ? (
                              <div className="flex items-center justify-center py-2">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                <span>加载更多...</span>
                              </div>
                            ) : hasMore ? (
                              <span className="text-xs opacity-70">向下滚动加载更多</span>
                            ) : (
                              <span className="text-xs opacity-70">已加载全部用户</span>
                            )}
                          </div>
                        )}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormDescription>
                团队管理员拥有管理团队成员的权限
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {isCreating && <>
          <FormField
            control={form.control}
            name="chat_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>聊天模型</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="请选择适当的模型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {chatOption?.map((option) => (
                      <SelectItem
                        key={option.llm_id}
                        value={option.llm_id}>
                        {option.llm_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="embd_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>嵌入模型</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="请选择适当的模型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {embdOption?.map((option) => (
                      <SelectItem
                        key={option.llm_id}
                        value={option.llm_id}>
                        {option.llm_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rerank_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rerank模型</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="请选择适当的模型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {rerankOption?.map((option) => (
                      <SelectItem
                        key={option.llm_id}
                        value={option.llm_id}>
                        {option.llm_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </>}

        {!viewOnly && (
          <div className="flex justify-end gap-2">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && (
                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              )}
              {isCreating ? "创建团队" : "保存修改"}
            </Button>
          </div>
        )}
      </form>
    </Form>
  )
}
