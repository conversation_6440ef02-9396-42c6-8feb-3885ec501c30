"use client"

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import { cn } from '@ragtop-web/ui/lib/utils'

// 注意：如果样式导入有问题，可以注释掉下面这行
// import 'highlight.js/styles/github.css'

interface MarkdownRendererProps {
  content: string
  className?: string
}

/**
 * Markdown 渲染器组件
 *
 * 支持 GitHub Flavored Markdown 和代码高亮
 */
export function MarkdownRenderer({ content, className }: MarkdownRendererProps) {
  return (
    <div className={cn("prose prose-sm dark:prose-invert max-w-none", className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // 自定义代码块样式
          code: ({ node, className, children, ...props }: any) => {
            const inline = !className?.includes('language-')
            const match = /language-(\w+)/.exec(className || '')
            return !inline && match ? (
              <pre className="bg-muted/50 border rounded-md p-4 overflow-x-auto my-4">
                <code className={cn("text-sm font-mono", className)} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className="bg-muted/50 px-1.5 py-0.5 rounded text-sm font-mono border" {...props}>
                {children}
              </code>
            )
          },
          // 自定义表格样式
          table: ({ children }) => (
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-border">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-border bg-muted px-4 py-2 text-left font-medium">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-border px-4 py-2">
              {children}
            </td>
          ),
          // 自定义链接样式
          a: ({ children, href }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              {children}
            </a>
          ),
          // 自定义引用块样式
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-primary pl-4 italic text-muted-foreground">
              {children}
            </blockquote>
          ),
          // 自定义列表样式
          ul: ({ children }) => (
            <ul className="list-disc list-inside space-y-1">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-1">
              {children}
            </ol>
          ),
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-semibold mb-3 mt-5 first:mt-0">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-medium mb-2 mt-4 first:mt-0">
              {children}
            </h3>
          ),
          // 自定义段落样式
          p: ({ children }) => (
            <p className="mb-3 leading-relaxed">
              {children}
            </p>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
