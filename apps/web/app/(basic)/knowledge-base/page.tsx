"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { PlusIcon } from "lucide-react"
import { Card, CardContent } from "@ragtop-web/ui/components/card"
import { KnowledgeBaseCard } from "./components/knowledge-base-card"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { KnowledgeBaseForm } from "./components/knowledge-base-form"
import { FileManagementDrawer } from "./components/file-management-drawer"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import {
  useKnowledgeBases,
  useCreateKnowledgeBase,
  useUpdateKnowledgeBase,
  useDeleteKnowledgeBase,
  useAddKnowledgeBaseDocuments,
  type KnowledgeBase,
  type KnowledgeBaseParams,
} from "@/service"

export type ParseStatus = "success" | "failed" | "parsing" | "none"

// 本地使用的知识库类型
export type LocalKnowledgeBase = {
  id: string
  name: string
  parser: "DeepDOC" | "MinerU"
  sliceMethod: string
  files: Array<{
    id: string
    name: string
    type: string
    size: string
    parseStatus: ParseStatus
    enabled?: boolean
  }>
}

export default function KnowledgeBasePage() {
  const { toast } = useToast()
  const router = useRouter()
  const [pageNumber, setPageNumber] = useState(1)
  const [pageSize] = useState(10)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [isCreating, setIsCreating] = useState(false)

  // 文件管理抽屉状态
  const [isFileDrawerOpen, setIsFileDrawerOpen] = useState(false)
  const [fileManagementKnowledgeBase, setFileManagementKnowledgeBase] = useState<KnowledgeBase | null>(null)

  // 获取知识库列表
  const { data: knowledgeBasesData, isLoading, error } = useKnowledgeBases(pageNumber, pageSize)
  const knowledgeBases = knowledgeBasesData?.records || []

  // 知识库操作hooks
  const createKnowledgeBase = useCreateKnowledgeBase()
  const updateKnowledgeBase = useUpdateKnowledgeBase()
  const deleteKnowledgeBase = useDeleteKnowledgeBase()
  const addDocuments = useAddKnowledgeBaseDocuments()

  // 处理打开知识库详情页面
  const handleOpenKnowledgeBase = (knowledgeBase: KnowledgeBase) => {
    // 导航到知识库详情页面
    router.push(`/knowledge-base/${knowledgeBase.id}`)
  }

  // 处理打开创建知识库抽屉
  const handleCreateKnowledgeBase = () => {
    setSelectedKnowledgeBase(null)
    setIsCreating(true)
    setIsDrawerOpen(true)
  }

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false)
  }

  // 处理保存知识库
  const handleSaveKnowledgeBase = (knowledgeBaseData: KnowledgeBaseParams) => {
    if (isCreating) {
      // 创建新知识库
      createKnowledgeBase.mutate(knowledgeBaseData, {
        onSuccess: (data) => {
          toast({
            title: "创建成功",
            description: `知识库 ${data.name} 已创建`,
          })
          setIsDrawerOpen(false)
        },
        onError: (error) => {
          toast({
            title: "创建失败",
            description: "创建知识库时发生错误",
            variant: "destructive",
          })
        }
      })
    } else if (selectedKnowledgeBase) {
      // 更新现有知识库
      updateKnowledgeBase.mutate(
        {
          ...knowledgeBaseData,
          kbase_id: selectedKnowledgeBase.id
        },
        {
          onSuccess: (data) => {
            toast({
              title: "更新成功",
              description: `知识库 ${data.name} 已更新`,
            })
            setIsDrawerOpen(false)
          },
          onError: (error) => {
            toast({
              title: "更新失败",
              description: "更新知识库时发生错误",
              variant: "destructive",
            })
          }
        }
      )
    }
  }

  // 处理删除知识库
  const handleDeleteKnowledgeBase = (kbaseId: string) => {
    deleteKnowledgeBase.mutate(
      { kbase_id: kbaseId },
      {
        onSuccess: () => {
          toast({
            title: "删除成功",
            description: "知识库已成功删除",
          })
        },
        onError: (error) => {
          toast({
            title: "删除失败",
            description: "删除知识库时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }

  // 处理打开文件管理抽屉
  const handleOpenFileManagement = (knowledgeBase: KnowledgeBase) => {
    setFileManagementKnowledgeBase(knowledgeBase)
    setIsFileDrawerOpen(true)
  }

  // 处理关闭文件管理抽屉
  const handleCloseFileManagement = () => {
    setIsFileDrawerOpen(false)
  }

  // 处理添加文件到知识库
  const handleAddFiles = (kbaseId: string, fileIds: string[]) => {
    if (!fileIds.length) return

    addDocuments.mutate(
      { kbase_id: kbaseId, file_ids: fileIds },
      {
        onSuccess: () => {
          toast({
            title: "添加成功",
            description: `已添加 ${fileIds.length} 个文件到知识库`,
          })
        },
        onError: (error) => {
          toast({
            title: "添加失败",
            description: "添加文件时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }

  // 处理文件解析
  const handleParseFiles = (fileIds: string[]) => {
    if (!fileManagementKnowledgeBase || !fileIds.length) return

    toast({
      title: "开始解析",
      description: `正在解析 ${fileIds.length} 个文件`,
    })

    // 实际应用中应该调用API来解析文件
    // 这里暂时使用模拟数据
    setTimeout(() => {
      toast({
        title: "解析完成",
        description: "文件解析已完成",
      })
    }, 2000)
  }

  return (
    <CustomContainer title="知识库管理">
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <span className="ml-2">加载中...</span>
        </div>
      ) : error ? (
        <div className="text-center py-12 text-destructive">
          加载知识库列表失败，请刷新页面重试
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 添加知识库卡片 */}
          <Card
            className="border-dashed cursor-pointer hover:border-primary/50 transition-colors"
            onClick={handleCreateKnowledgeBase}
          >
            <CardContent className="flex items-center justify-center h-20">
              <div className="flex flex-col items-center text-muted-foreground">
                <PlusIcon className="h-10 w-10 mb-2" />
                <span className="text-lg">添加新知识库</span>
              </div>
            </CardContent>
          </Card>

          {/* 知识库卡片列表 */}
          {knowledgeBases.map((knowledgeBase) => (
            <KnowledgeBaseCard
              key={knowledgeBase.id}
              knowledgeBase={knowledgeBase}
              onClick={() => handleOpenKnowledgeBase(knowledgeBase)}
              onDelete={() => handleDeleteKnowledgeBase(knowledgeBase.id)}
              onManageFiles={() => handleOpenFileManagement(knowledgeBase)}
            />
          ))}
        </div>
      )}

      {/* 知识库抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={isCreating ? "创建知识库" : "编辑知识库"}
      >
        <KnowledgeBaseForm
          knowledgeBase={isCreating ? undefined : {
            id: selectedKnowledgeBase?.id || "",
            name: selectedKnowledgeBase?.name || "",
            parser: selectedKnowledgeBase?.parser_id as "DeepDOC" | "MinerU" || "DeepDOC",
            sliceMethod: selectedKnowledgeBase?.sliceMethod || "naive",
            // files: []
          }}
          onSave={(formData) => {
            handleSaveKnowledgeBase(formData)
          }}
          isLoading={createKnowledgeBase?.isPending || updateKnowledgeBase?.isPending}
          isCreating={isCreating}
        />
      </CustomDrawer>

      {/* 文件管理抽屉 */}
      {fileManagementKnowledgeBase && (
        <FileManagementDrawer
          open={isFileDrawerOpen}
          onClose={handleCloseFileManagement}
          knowledgeBase={{
            id: fileManagementKnowledgeBase.id,
            name: fileManagementKnowledgeBase.name,
            parser: fileManagementKnowledgeBase.parser_id as "DeepDOC" | "MinerU",
            sliceMethod: fileManagementKnowledgeBase.sliceMethod,
            files: [] // 文件列表会在详情页加载
          }}
          onParseFiles={(fileIds) => handleParseFiles(fileIds)}
          // onAddFiles={(fileIds:string[]) => handleAddFiles(fileManagementKnowledgeBase.id, fileIds)}
        />
      )}

    </CustomContainer>
  )
}
